using AIEvent.Application.Constants;
using AIEvent.Application.DTO.Common;
using AIEvent.Application.DTO.User;
using AIEvent.Application.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace AIEvent.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class UserController : ControllerBase
    {
        private readonly IUserService _userService;

        public UserController(IUserService userService)
        {
            _userService = userService;
        }

        [HttpGet("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> GetUser(string id)
        {
            try
            {
                var result = await _userService.GetUserByIdAsync(id);

                if (result == null)
                {
                    return NotFound(ErrorResponse.FailureResult(
                        "User not found",
                        statusCode: ErrorCodes.NotFound));
                }

                return Ok(SuccessReponse<UserResponse>.SuccessResult(
                    result,
                    message: "User retrieved successfully"));
            }
            catch (Exception)
            {
                return StatusCode(500, ErrorResponse.FailureResult(
                    "An internal server error occurred",
                    statusCode: ErrorCodes.InternalServerError));
            }
        }

        [HttpGet("profile")]
        public async Task<IActionResult> GetProfile()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(ErrorResponse.FailureResult(
                        "User not found",
                        ["Invalid user token"],
                        ErrorCodes.Unauthorized));
                }

                var result = await _userService.GetUserByIdAsync(userId);

                if (result == null)
                {
                    return NotFound(ErrorResponse.FailureResult(
                        "User not found",
                        statusCode: ErrorCodes.NotFound));
                }

                return Ok(SuccessReponse<UserResponse>.SuccessResult(
                    result,
                    message: "Profile retrieved successfully"));
            }
            catch (Exception)
            {
                return StatusCode(500, ErrorResponse.FailureResult(
                    "An internal server error occurred",
                    statusCode: ErrorCodes.InternalServerError));
            }
        }

        [HttpPut("profile")]
        public async Task<IActionResult> UpdateProfile([FromBody] UpdateUserRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList();
                    return BadRequest(ErrorResponse.FailureResult(
                        "Invalid input data",
                        errors,
                        ErrorCodes.ValidationFailed));
                }

                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(ErrorResponse.FailureResult(
                        "User not found",
                        ["Invalid user token"],
                        ErrorCodes.Unauthorized));
                }

                var result = await _userService.UpdateUserAsync(userId, request);

                if (result == null)
                {
                    return BadRequest(ErrorResponse.FailureResult(
                        "Failed to update user profile",
                        statusCode: ErrorCodes.InvalidInput));
                }

                return Ok(SuccessReponse<UserResponse>.SuccessResult(
                    result,
                    SuccessCodes.Updated,
                    "Profile updated successfully"));
            }
            catch (Exception)
            {
                return StatusCode(500, ErrorResponse.FailureResult(
                    "An internal server error occurred",
                    statusCode: ErrorCodes.InternalServerError));
            }
        }

        [HttpGet]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> GetAllUsers([FromQuery] int page = 1, [FromQuery] int pageSize = 10)
        {
            try
            {
                var result = await _userService.GetAllUsersAsync(page, pageSize);
                return Ok(SuccessReponse<List<UserResponse>>.SuccessResult(
                    result,
                    message: "Users retrieved successfully"));
            }
            catch (Exception)
            {
                return StatusCode(500, ErrorResponse.FailureResult(
                    "An internal server error occurred",
                    statusCode: ErrorCodes.InternalServerError));
            }
        }
    }
}
