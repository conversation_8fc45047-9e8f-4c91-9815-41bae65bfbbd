using AIEvent.Application.DTO.User;
using AIEvent.Application.Services.Interfaces;
using AIEvent.Domain.Identity;
using AutoMapper;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace AIEvent.Application.Services.Implements
{
    public class UserService : IUserService
    {
        private readonly UserManager<AppUser> _userManager;
        private readonly IMapper _mapper;

        public UserService(
            UserManager<AppUser> userManager,
            IMapper mapper)
        {
            _userManager = userManager;
            _mapper = mapper;
        }

        public async Task<UserResponse> GetUserByIdAsync(string userId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null || !user.IsActive)
                {
                    return null;
                }

                var userResponse = _mapper.Map<UserResponse>(user);
                var roles = await _userManager.GetRolesAsync(user);
                userResponse.Roles = [.. roles];

                return userResponse;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<UserResponse> UpdateUserAsync(string userId, UpdateUserRequest request)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null || !user.IsActive)
                {
                    return null;
                }

                // Map update request to existing user
                _mapper.Map(request, user);

                var result = await _userManager.UpdateAsync(user);
                if (!result.Succeeded)
                {
                    return null;
                }

                var userResponse = _mapper.Map<UserResponse>(user);
                var roles = await _userManager.GetRolesAsync(user);
                userResponse.Roles = [.. roles];

                return userResponse;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<UserResponse>> GetAllUsersAsync(int page = 1, int pageSize = 10)
        {
            try
            {
                var users = await _userManager.Users
                    .Where(u => u.IsActive)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var userResponses = new List<UserResponse>();
                foreach (var user in users)
                {
                    var userResponse = _mapper.Map<UserResponse>(user);
                    var roles = await _userManager.GetRolesAsync(user);
                    userResponse.Roles = [.. roles];
                    userResponses.Add(userResponse);
                }

                return userResponses;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

    }
}
