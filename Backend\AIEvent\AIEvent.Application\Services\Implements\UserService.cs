using AIEvent.Application.Constants;
using AIEvent.Application.DTO.Auth;
using AIEvent.Application.DTO.User;
using AIEvent.Application.Extensions;
using AIEvent.Application.Services.Interfaces;
using AIEvent.Domain.Identity;
using AutoMapper;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace AIEvent.Application.Services.Implements
{
    public class UserService : IUserService
    {
        private readonly UserManager<AppUser> _userManager;
        private readonly RoleManager<AppRole> _roleManager;
        private readonly IMapper _mapper;

        public UserService(
            UserManager<AppUser> userManager,
            RoleManager<AppRole> roleManager,
            IMapper mapper)
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _mapper = mapper;
        }

        public async Task<UserResponse> GetUserByIdAsync(string userId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null || !user.IsActive)
                {
                    return null;
                }

                return await _mapper.MapToUserResponseAsync(user, _userManager);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<UserResponse> UpdateUserAsync(string userId, UpdateUserRequest request)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null || !user.IsActive)
                {
                    return null;
                }

                // Use extension method to update user
                _mapper.UpdateUserFromRequest(request, user);

                var result = await _userManager.UpdateAsync(user);
                if (!result.Succeeded)
                {
                    return null;
                }

                return await _mapper.MapToUserResponseAsync(user, _userManager);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<UserResponse>> GetAllUsersAsync(int page = 1, int pageSize = 10)
        {
            try
            {
                var users = await _userManager.Users
                    .Where(u => u.IsActive)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                return await _mapper.MapToUserResponsesAsync(users, _userManager);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

    }
}
