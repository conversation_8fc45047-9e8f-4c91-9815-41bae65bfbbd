using AIEvent.Domain.Identity;
using AIEvent.Infrastructure.Context;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace AIEvent.Infrastructure.SeedData
{
    public class DataSeeder
    {
        private readonly DatabaseContext _context;
        private readonly UserManager<AppUser> _userManager;
        private readonly RoleManager<AppRole> _roleManager;
        private readonly ILogger<DataSeeder> _logger;

        public DataSeeder(
            DatabaseContext context,
            UserManager<AppUser> userManager,
            RoleManager<AppRole> roleManager,
            ILogger<DataSeeder> logger)
        {
            _context = context;
            _userManager = userManager;
            _roleManager = roleManager;
            _logger = logger;
        }

        public async Task SeedAsync()
        {
            try
            {
                // Ensure database is created
                await _context.Database.EnsureCreatedAsync();

                // Seed roles first
                await SeedRolesAsync();

                // Seed users
                await SeedUsersAsync();

                _logger.LogInformation("Data seeding completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while seeding data");
                throw;
            }
        }

        private async Task SeedRolesAsync()
        {
            foreach (var roleInfo in SeedDataConstants.Roles.RoleDescriptions)
            {
                var existingRole = await _roleManager.FindByNameAsync(roleInfo.Key);
                if (existingRole == null)
                {
                    var role = new AppRole
                    {
                        Name = roleInfo.Key,
                        Description = roleInfo.Value,
                        CreatedAt = DateTime.UtcNow
                    };

                    var result = await _roleManager.CreateAsync(role);
                    if (result.Succeeded)
                    {
                        _logger.LogInformation($"Role '{roleInfo.Key}' created successfully");
                    }
                    else
                    {
                        _logger.LogError($"Failed to create role '{roleInfo.Key}': {string.Join(", ", result.Errors.Select(e => e.Description))}");
                    }
                }
                else
                {
                    _logger.LogInformation($"Role '{roleInfo.Key}' already exists");
                }
            }
        }

        private async Task SeedUsersAsync()
        {
            foreach (var userInfo in SeedDataConstants.Users.DefaultUsers)
            {
                var existingUser = await _userManager.FindByEmailAsync(userInfo.Email);
                if (existingUser == null)
                {
                    var user = new AppUser
                    {
                        UserName = userInfo.Email,
                        Email = userInfo.Email,
                        FullName = userInfo.FullName,
                        PhoneNumber = userInfo.PhoneNumber,
                        EmailConfirmed = userInfo.EmailConfirmed,
                        PhoneNumberConfirmed = userInfo.PhoneNumberConfirmed,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow
                    };

                    var result = await _userManager.CreateAsync(user, userInfo.Password);
                    if (result.Succeeded)
                    {
                        // Add roles to user
                        foreach (var roleName in userInfo.Roles)
                        {
                            if (await _roleManager.RoleExistsAsync(roleName))
                            {
                                await _userManager.AddToRoleAsync(user, roleName);
                                _logger.LogInformation($"Added role '{roleName}' to user '{userInfo.Email}'");
                            }
                        }

                        _logger.LogInformation($"User '{userInfo.Email}' created successfully");
                    }
                    else
                    {
                        _logger.LogError($"Failed to create user '{userInfo.Email}': {string.Join(", ", result.Errors.Select(e => e.Description))}");
                    }
                }
                else
                {
                    _logger.LogInformation($"User '{userInfo.Email}' already exists");
                }
            }
        }
    }
}
