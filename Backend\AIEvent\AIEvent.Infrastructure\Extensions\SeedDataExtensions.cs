using AIEvent.Infrastructure.SeedData;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace AIEvent.Infrastructure.Extensions
{
    public static class SeedDataExtensions
    {
        /// <summary>
        /// Seeds the database with initial data
        /// </summary>
        /// <param name="app">The web application</param>
        /// <returns>The web application for chaining</returns>
        public static async Task<WebApplication> SeedDataAsync(this WebApplication app)
        {
            using var scope = app.Services.CreateScope();
            var services = scope.ServiceProvider;
            var logger = services.GetRequiredService<ILogger<DataSeeder>>();

            try
            {
                logger.LogInformation("Starting database seeding...");
                
                var seeder = services.GetRequiredService<DataSeeder>();
                await seeder.SeedAsync();
                
                logger.LogInformation("Database seeding completed successfully");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while seeding the database");
                throw;
            }

            return app;
        }

        /// <summary>
        /// Registers the DataSeeder service
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddDataSeeder(this IServiceCollection services)
        {
            services.AddScoped<DataSeeder>();
            return services;
        }
    }
}
