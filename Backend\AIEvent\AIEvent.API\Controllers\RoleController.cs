using AIEvent.Application.Constants;
using AIEvent.Application.DTO.Common;
using AIEvent.Application.DTO.Role;
using AIEvent.Application.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AIEvent.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "Admin")]
    public class RoleController : ControllerBase
    {
        private readonly IRoleService _roleService;

        public RoleController(IRoleService roleService)
        {
            _roleService = roleService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAllRoles()
        {
            try
            {
                var result = await _roleService.GetAllRolesAsync();
                return Ok(SuccessReponse<List<RoleResponse>>.SuccessResult(
                    result,
                    message: "Roles retrieved successfully"));
            }
            catch (Exception)
            {
                return StatusCode(500, ErrorResponse.FailureResult(
                    "An internal server error occurred",
                    statusCode: ErrorCodes.InternalServerError));
            }
        }



        [HttpPost]
        public async Task<IActionResult> CreateRole([FromBody] CreateRoleRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList();
                    return BadRequest(ErrorResponse.FailureResult(
                        "Invalid input data",
                        errors,
                        ErrorCodes.ValidationFailed));
                }

                var result = await _roleService.CreateRoleAsync(request);

                if (result == null)
                {
                    return BadRequest(ErrorResponse.FailureResult(
                        "Failed to create role. Role name may already exist.",
                        statusCode: ErrorCodes.InvalidInput));
                }

                return Ok(SuccessReponse<RoleResponse>.SuccessResult(
                    result,
                    SuccessCodes.Created,
                    "Role created successfully"));
            }
            catch (Exception)
            {
                return StatusCode(500, ErrorResponse.FailureResult(
                    "An internal server error occurred",
                    statusCode: ErrorCodes.InternalServerError));
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateRole(string id, [FromBody] UpdateRoleRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList();
                    return BadRequest(ErrorResponse.FailureResult(
                        "Invalid input data",
                        errors,
                        ErrorCodes.ValidationFailed));
                }

                var result = await _roleService.UpdateRoleAsync(id, request);

                if (result == null)
                {
                    return NotFound(ErrorResponse.FailureResult(
                        "Role not found",
                        statusCode: ErrorCodes.NotFound));
                }

                return Ok(SuccessReponse<RoleResponse>.SuccessResult(
                    result,
                    SuccessCodes.Updated,
                    "Role updated successfully"));
            }
            catch (Exception)
            {
                return StatusCode(500, ErrorResponse.FailureResult(
                    "An internal server error occurred",
                    statusCode: ErrorCodes.InternalServerError));
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteRole(string id)
        {
            try
            {
                var result = await _roleService.DeleteRoleAsync(id);

                if (!result)
                {
                    return BadRequest(ErrorResponse.FailureResult(
                        "Failed to delete role. Role may not exist or may have users assigned.",
                        statusCode: ErrorCodes.InvalidInput));
                }

                return Ok(SuccessReponse<object>.SuccessResult(
                    new { },
                    SuccessCodes.Deleted,
                    "Role deleted successfully"));
            }
            catch (Exception)
            {
                return StatusCode(500, ErrorResponse.FailureResult(
                    "An internal server error occurred",
                    statusCode: ErrorCodes.InternalServerError));
            }
        }
    }
}
