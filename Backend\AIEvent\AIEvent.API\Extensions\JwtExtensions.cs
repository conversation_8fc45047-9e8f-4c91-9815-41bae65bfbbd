﻿using AIEvent.Application.Constants;
using AIEvent.Application.DTO.Common;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using System.Text.Json;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace AIEvent.API.Extensions
{
    public static class JwtExtensions
    {
        public static void AddJwtAuthentication(this IServiceCollection services, IConfiguration configuration)
        {
            var jwtSettings = configuration.GetSection("Jwt");
            var key = Encoding.ASCII.GetBytes(jwtSettings["Key"]);

            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
               options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidIssuer = jwtSettings["Issuer"],
                    ValidAudience = jwtSettings["Audience"],
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero
               };

                options.Events = new JwtBearerEvents
                {
                    OnChallenge = context =>
                    {
                        context.HandleResponse();
                        var error = ErrorResponse.FailureResult(
                            ErrorMessages.Unauthorized,
                            statusCode: ErrorCodes.Unauthorized
                        );
                        context.Response.StatusCode = 401;
                        context.Response.ContentType = "application/json";
                        var result = JsonSerializer.Serialize(error);
                        return context.Response.WriteAsync(result);
                    },
                    OnForbidden = context =>
                    {
                        var error = ErrorResponse.FailureResult(
                            ErrorMessages.Forbidden,
                            statusCode: ErrorCodes.PermissionDenied
                        );
                        context.Response.StatusCode = 403;
                        context.Response.ContentType = "application/json"; 
                        var result = JsonSerializer.Serialize(error); 
                        return context.Response.WriteAsync(result);
                    }
                };

            });
        }
    }
}
