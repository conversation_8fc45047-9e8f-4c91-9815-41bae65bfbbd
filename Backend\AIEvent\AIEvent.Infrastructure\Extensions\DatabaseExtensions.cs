using AIEvent.Infrastructure.Context;
using Microsoft.AspNetCore.Builder;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace AIEvent.Infrastructure.Extensions
{
    public static class DatabaseExtensions
    {
        /// <summary>
        /// Ensures the database is created and migrations are applied
        /// </summary>
        /// <param name="app">The web application</param>
        /// <returns>The web application for chaining</returns>
        public static async Task<WebApplication> EnsureDatabaseAsync(this WebApplication app)
        {
            using var scope = app.Services.CreateScope();
            var services = scope.ServiceProvider;
            var logger = services.GetRequiredService<ILogger<DatabaseContext>>();

            try
            {
                logger.LogInformation("Ensuring database is created and up to date...");
                
                var context = services.GetRequiredService<DatabaseContext>();
                
                // Ensure database is created
                await context.Database.EnsureCreatedAsync();
                
                // Apply any pending migrations
                if (context.Database.GetPendingMigrations().Any())
                {
                    logger.LogInformation("Applying pending migrations...");
                    await context.Database.MigrateAsync();
                }
                
                logger.LogInformation("Database is ready");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while ensuring database");
                throw;
            }

            return app;
        }

        /// <summary>
        /// Drops and recreates the database (Development only)
        /// </summary>
        /// <param name="app">The web application</param>
        /// <returns>The web application for chaining</returns>
        public static async Task<WebApplication> RecreateDatabase(this WebApplication app)
        {
            if (!app.Environment.IsDevelopment())
            {
                throw new InvalidOperationException("Database recreation is only allowed in development environment");
            }

            using var scope = app.Services.CreateScope();
            var services = scope.ServiceProvider;
            var logger = services.GetRequiredService<ILogger<DatabaseContext>>();

            try
            {
                logger.LogWarning("Recreating database (Development only)...");
                
                var context = services.GetRequiredService<DatabaseContext>();
                
                // Drop database if exists
                await context.Database.EnsureDeletedAsync();
                
                // Create database
                await context.Database.EnsureCreatedAsync();
                
                logger.LogInformation("Database recreated successfully");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while recreating database");
                throw;
            }

            return app;
        }
    }
}
